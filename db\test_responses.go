package db

import (
	"context"
	"fmt"
	"log/slog"
	"time"
	"ziaacademy-backend/internal/models"
)

// RecordTestResponses records all responses for a test from a student
func (p *DbPlugin) RecordTestResponses(ctx context.Context, studentID uint, testResponsesInput *models.TestResponsesForCreate) (*models.TestResponsesResult, error) {
	start := time.Now()

	slog.Info("Recording test responses",
		"student_id", studentID,
		"test_id", testResponsesInput.TestID,
		"response_count", len(testResponsesInput.Responses),
	)

	// Verify the test exists and is active (read-only operation, no transaction needed)
	var test models.Test
	if err := p.db.Preload("Sections.Questions.Options").First(&test, testResponsesInput.TestID).Error; err != nil {
		duration := time.Since(start)
		slog.Error("Test not found for response recording",
			"student_id", studentID,
			"test_id", testResponsesInput.TestID,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, fmt.Errorf("test with ID %d not found: %w", testResponsesInput.TestID, err)
	}

	if !test.Active {
		duration := time.Since(start)
		slog.Warn("Attempt to submit responses to inactive test",
			"student_id", studentID,
			"test_id", testResponsesInput.TestID,
			"duration_ms", duration.Milliseconds(),
		)
		return nil, fmt.Errorf("test with ID %d is not active", testResponsesInput.TestID)
	}

	// Verify the student exists (read-only operation, no transaction needed)
	var student models.Student
	if err := p.db.First(&student, studentID).Error; err != nil {
		duration := time.Since(start)
		slog.Error("Student not found for response recording",
			"student_id", studentID,
			"test_id", testResponsesInput.TestID,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, fmt.Errorf("student with ID %d not found: %w", studentID, err)
	}

	// Check if student has already submitted responses for this test (read-only operation)
	var existingResponseCount int64
	if err := p.db.Model(&models.TestResponse{}).
		Where("student_id = ? AND test_id = ?", studentID, testResponsesInput.TestID).
		Count(&existingResponseCount).Error; err != nil {
		return nil, fmt.Errorf("failed to check existing responses: %w", err)
	}

	if existingResponseCount > 0 {
		duration := time.Since(start)
		slog.Warn("Student already submitted responses for this test",
			"student_id", studentID,
			"test_id", testResponsesInput.TestID,
			"existing_count", existingResponseCount,
			"duration_ms", duration.Milliseconds(),
		)
		return nil, fmt.Errorf("student has already submitted responses for test ID %d", testResponsesInput.TestID)
	}

	// Create a map of question ID to question for quick lookup
	questionMap := make(map[uint]models.Question)
	for _, section := range test.Sections {
		for _, question := range section.Questions {
			questionMap[question.ID] = question
		}
	}

	// Validate that all questions in the responses belong to this test
	for _, responseInput := range testResponsesInput.Responses {
		if _, exists := questionMap[responseInput.QuestionID]; !exists {
			return nil, fmt.Errorf("question with ID %d is not part of test %d", responseInput.QuestionID, testResponsesInput.TestID)
		}
	}

	// Now start transaction for actual write operations
	tx := p.db.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// Process each response with immediate evaluation
	var responseResults []models.TestResponseResult
	totalScore := 0
	correctAnswers := 0

	for _, responseInput := range testResponsesInput.Responses {
		// Get the question for evaluation
		question, exists := questionMap[responseInput.QuestionID]
		if !exists {
			tx.Rollback()
			duration := time.Since(start)
			slog.Error("Question not found in test during response recording",
				"student_id", studentID,
				"test_id", testResponsesInput.TestID,
				"question_id", responseInput.QuestionID,
				"duration_ms", duration.Milliseconds(),
			)
			return nil, fmt.Errorf("question %d not found in test %d", responseInput.QuestionID, testResponsesInput.TestID)
		}

		// Create the test response record
		testResponse := models.TestResponse{
			StudentID:         studentID,
			TestID:            testResponsesInput.TestID,
			QuestionID:        responseInput.QuestionID,
			SelectedOptionIDs: responseInput.SelectedOptionIDs,
			ResponseText:      responseInput.ResponseText,
		}

		// Evaluate the response immediately
		isCorrect, calculatedScore := p.evaluateResponse(&question, &testResponse)
		testResponse.IsCorrect = isCorrect
		testResponse.CalculatedScore = &calculatedScore

		// Update totals
		totalScore += calculatedScore
		if isCorrect {
			correctAnswers++
		}

		// Save the response with evaluation results
		if err := tx.Create(&testResponse).Error; err != nil {
			tx.Rollback()
			duration := time.Since(start)
			slog.Error("Failed to save test response",
				"student_id", studentID,
				"test_id", testResponsesInput.TestID,
				"question_id", responseInput.QuestionID,
				"error", err.Error(),
				"duration_ms", duration.Milliseconds(),
			)
			return nil, fmt.Errorf("failed to save response for question %d: %w", responseInput.QuestionID, err)
		}

		// Add to results with evaluation data
		responseResult := models.TestResponseResult{
			QuestionID:      responseInput.QuestionID,
			IsCorrect:       isCorrect,
			CalculatedScore: &calculatedScore,
			Message:         "Response recorded and evaluated successfully",
		}

		responseResults = append(responseResults, responseResult)

		slog.Debug("Response evaluated during recording",
			"student_id", studentID,
			"test_id", testResponsesInput.TestID,
			"question_id", responseInput.QuestionID,
			"is_correct", isCorrect,
			"calculated_score", calculatedScore,
		)
	}

	// Commit the transaction
	if err := tx.Commit().Error; err != nil {
		duration := time.Since(start)
		slog.Error("Failed to commit test responses transaction",
			"student_id", studentID,
			"test_id", testResponsesInput.TestID,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, fmt.Errorf("failed to commit responses: %w", err)
	}

	duration := time.Since(start)
	slog.Info("Test responses recorded and evaluated successfully",
		"student_id", studentID,
		"test_id", testResponsesInput.TestID,
		"total_questions", len(testResponsesInput.Responses),
		"correct_answers", correctAnswers,
		"total_score", totalScore,
		"duration_ms", duration.Milliseconds(),
	)

	// Prepare the result with evaluation data
	result := &models.TestResponsesResult{
		TestID:          testResponsesInput.TestID,
		StudentID:       studentID,
		TotalQuestions:  len(testResponsesInput.Responses),
		CorrectAnswers:  correctAnswers,
		TotalScore:      totalScore,
		ResponseResults: responseResults,
		Message:         fmt.Sprintf("Successfully recorded and evaluated %d responses. Score: %d/%d correct answers.", len(testResponsesInput.Responses), correctAnswers, len(testResponsesInput.Responses)),
	}

	return result, nil
}

// GetStudentTestResponses retrieves all responses for a specific student and test with enhanced information
func (p *DbPlugin) GetStudentTestResponses(ctx context.Context, studentID, testID uint) (*models.StudentTestResponsesResult, error) {
	start := time.Now()

	slog.Debug("Getting enhanced student test responses",
		"student_id", studentID,
		"test_id", testID,
	)

	// Get the test information
	var test models.Test
	if err := p.db.First(&test, testID).Error; err != nil {
		duration := time.Since(start)
		slog.Error("Failed to retrieve test information",
			"student_id", studentID,
			"test_id", testID,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, fmt.Errorf("failed to retrieve test information: %w", err)
	}

	// Get student information
	var student models.Student
	if err := p.db.Preload("User").First(&student, studentID).Error; err != nil {
		duration := time.Since(start)
		slog.Error("Failed to retrieve student information",
			"student_id", studentID,
			"test_id", testID,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, fmt.Errorf("failed to retrieve student information: %w", err)
	}

	// Get all responses for the student and test
	var responses []models.TestResponse
	if err := p.db.Preload("Question").Preload("Student.User").Preload("Test").
		Where("student_id = ? AND test_id = ?", studentID, testID).
		Find(&responses).Error; err != nil {
		duration := time.Since(start)
		slog.Error("Failed to retrieve student test responses",
			"student_id", studentID,
			"test_id", testID,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, fmt.Errorf("failed to retrieve responses: %w", err)
	}

	if len(responses) == 0 {
		duration := time.Since(start)
		slog.Info("No responses found for student and test",
			"student_id", studentID,
			"test_id", testID,
			"duration_ms", duration.Milliseconds(),
		)
		return &models.StudentTestResponsesResult{
			TestID:              testID,
			TestName:            test.Name,
			StudentID:           studentID,
			StudentName:         student.User.FullName,
			TotalScore:          0,
			ResponsesRecordedAt: time.Time{}, // Zero time if no responses
			Responses:           []models.TestResponse{},
			Message:             "No responses found for this test",
		}, nil
	}

	// Find the earliest recorded time (when responses were first submitted)
	var earliestRecordedAt time.Time = responses[0].CreatedAt
	for _, response := range responses {
		if response.CreatedAt.Before(earliestRecordedAt) {
			earliestRecordedAt = response.CreatedAt
		}
	}

	// Get total score from StudentTestMark table if available
	var totalScore int
	var studentTestMark models.StudentTestMark
	if err := p.db.Where("student_id = ? AND test_id = ?", studentID, testID).First(&studentTestMark).Error; err == nil {
		// Use the final marks from StudentTestMark table
		totalScore = studentTestMark.FinalMarks
		slog.Debug("Using total score from StudentTestMark table",
			"student_id", studentID,
			"test_id", testID,
			"final_marks", studentTestMark.FinalMarks,
		)
	} else {
		// StudentTestMark not found, return 0 score
		totalScore = 0
		slog.Debug("StudentTestMark not found, returning zero score",
			"student_id", studentID,
			"test_id", testID,
		)
	}

	duration := time.Since(start)
	slog.Debug("Enhanced student test responses retrieved successfully",
		"student_id", studentID,
		"test_id", testID,
		"response_count", len(responses),
		"total_score", totalScore,
		"recorded_at", earliestRecordedAt,
		"duration_ms", duration.Milliseconds(),
	)

	result := &models.StudentTestResponsesResult{
		TestID:              testID,
		TestName:            test.Name,
		StudentID:           studentID,
		StudentName:         student.User.FullName,
		TotalScore:          totalScore,
		ResponsesRecordedAt: earliestRecordedAt,
		Responses:           responses,
		Message:             fmt.Sprintf("Retrieved %d responses with total score: %d", len(responses), totalScore),
	}

	return result, nil
}

// GetTestRankings retrieves rankings for all students in a specific test
func (p *DbPlugin) GetTestRankings(ctx context.Context, testID uint, limit, offset int) (*models.TestRankingResult, error) {
	start := time.Now()

	slog.Info("Getting test rankings",
		"test_id", testID,
		"limit", limit,
		"offset", offset,
	)

	// Verify the test exists and get test details (read-only operation)
	var test models.Test
	if err := p.db.First(&test, testID).Error; err != nil {
		duration := time.Since(start)
		slog.Error("Test not found for rankings",
			"test_id", testID,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, fmt.Errorf("test with ID %d not found: %w", testID, err)
	}

	// Get all student marks for this test, ordered by final marks descending
	var studentMarks []models.StudentTestMark
	query := p.db.Preload("Student.User").
		Where("test_id = ?", testID).
		Order("final_marks DESC, student_id ASC") // Secondary sort by student_id for consistent ordering

	// Apply pagination if specified
	if limit > 0 {
		query = query.Limit(limit)
	}
	if offset > 0 {
		query = query.Offset(offset)
	}

	if err := query.Find(&studentMarks).Error; err != nil {
		duration := time.Since(start)
		slog.Error("Failed to retrieve student marks for rankings",
			"test_id", testID,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, fmt.Errorf("failed to retrieve student marks: %w", err)
	}

	if len(studentMarks) == 0 {
		duration := time.Since(start)
		slog.Info("No student marks found for test",
			"test_id", testID,
			"duration_ms", duration.Milliseconds(),
		)
		return &models.TestRankingResult{
			TestID:          testID,
			TestName:        test.Name,
			TotalStudents:   0,
			SubjectScores:   make(map[string]models.SubjectScoreInfo),
			StudentRankings: []models.StudentRankingInfo{},
			Message:         "No student marks found for this test",
		}, nil
	}

	// Calculate statistics
	var totalMarks int
	highestMarks := studentMarks[0].FinalMarks
	lowestMarks := studentMarks[len(studentMarks)-1].FinalMarks

	for _, mark := range studentMarks {
		totalMarks += mark.FinalMarks
		if mark.FinalMarks > highestMarks {
			highestMarks = mark.FinalMarks
		}
		if mark.FinalMarks < lowestMarks {
			lowestMarks = mark.FinalMarks
		}
	}

	averageMarks := float64(totalMarks) / float64(len(studentMarks))

	// Get total count for percentile calculation (if pagination is used)
	var totalStudents int64
	if err := p.db.Model(&models.StudentTestMark{}).
		Where("test_id = ?", testID).
		Count(&totalStudents).Error; err != nil {
		return nil, fmt.Errorf("failed to count total students: %w", err)
	}

	// Calculate subject-wise and topic-wise average scores from stored marks
	subjectScores := p.calculateSubjectScoresFromMarks(studentMarks)

	// Build student rankings with rank and percentile
	var studentRankings []models.StudentRankingInfo
	currentRank := offset + 1 // Start rank based on offset

	for i, mark := range studentMarks {
		// Handle tied scores - students with same marks get same rank
		if i > 0 && mark.FinalMarks < studentMarks[i-1].FinalMarks {
			currentRank = offset + i + 1
		}

		// Calculate percentile (percentage of students with lower scores)
		percentile := float64(int(totalStudents)-currentRank+1) / float64(totalStudents) * 100

		studentRanking := models.StudentRankingInfo{
			StudentID:          uint(mark.StudentID),
			StudentName:        mark.Student.User.FullName,
			StudentEmail:       mark.Student.User.Email,
			TotalPositiveMarks: mark.TotalPositiveMarks,
			TotalNegativeMarks: mark.TotalNegativeMarks,
			FinalMarks:         mark.FinalMarks,
			Rank:               currentRank,
			Percentile:         percentile,
		}

		studentRankings = append(studentRankings, studentRanking)
	}

	duration := time.Since(start)
	slog.Info("Test rankings retrieved successfully",
		"test_id", testID,
		"total_students", totalStudents,
		"returned_count", len(studentRankings),
		"highest_marks", highestMarks,
		"lowest_marks", lowestMarks,
		"average_marks", averageMarks,
		"duration_ms", duration.Milliseconds(),
	)

	// Prepare the result
	result := &models.TestRankingResult{
		TestID:          testID,
		TestName:        test.Name,
		TotalStudents:   int(totalStudents),
		HighestMarks:    highestMarks,
		LowestMarks:     lowestMarks,
		AverageMarks:    averageMarks,
		SubjectScores:   subjectScores,
		StudentRankings: studentRankings,
		Message:         fmt.Sprintf("Retrieved rankings for %d students (showing %d)", totalStudents, len(studentRankings)),
	}

	return result, nil
}

// evaluateResponse evaluates a student's response and calculates the score
func (p *DbPlugin) evaluateResponse(question *models.Question, response *models.TestResponse) (bool, int) {
	// For multiple choice questions, check selected options
	if len(response.SelectedOptionIDs) > 0 {
		return p.evaluateMultipleChoiceResponse(question, response)
	}

	// For text-based questions, compare with correct answer
	if response.ResponseText != nil {
		return p.evaluateTextResponse(question, response)
	}

	// No response provided
	return false, 0
}

// evaluateMultipleChoiceResponse evaluates multiple choice responses
func (p *DbPlugin) evaluateMultipleChoiceResponse(question *models.Question, response *models.TestResponse) (bool, int) {
	// Load question options if not already loaded
	if len(question.Options) == 0 {
		if err := p.db.Where("question_id = ?", question.ID).Find(&question.Options).Error; err != nil {
			slog.Error("Failed to load question options for evaluation",
				"question_id", question.ID,
				"error", err.Error(),
			)
			return false, 0
		}
	}

	// Get correct option IDs
	var correctOptionIDs []int
	for _, option := range question.Options {
		if option.IsCorrect {
			correctOptionIDs = append(correctOptionIDs, int(option.ID))
		}
	}

	// Compare selected options with correct options
	if len(correctOptionIDs) == 0 {
		slog.Warn("No correct options found for question", "question_id", question.ID)
		return false, 0
	}

	// Check if selected options match correct options exactly
	if len(response.SelectedOptionIDs) != len(correctOptionIDs) {
		return false, 0
	}

	// Create maps for comparison
	selectedMap := make(map[int]bool)
	for _, id := range response.SelectedOptionIDs {
		selectedMap[id] = true
	}

	correctMap := make(map[int]bool)
	for _, id := range correctOptionIDs {
		correctMap[id] = true
	}

	// Check if all selected options are correct and all correct options are selected
	for _, id := range response.SelectedOptionIDs {
		if !correctMap[id] {
			return false, 0
		}
	}

	for _, id := range correctOptionIDs {
		if !selectedMap[id] {
			return false, 0
		}
	}

	// All correct - return positive score (could be configurable based on question difficulty)
	return true, 1
}

// evaluateTextResponse evaluates text-based responses
func (p *DbPlugin) evaluateTextResponse(question *models.Question, response *models.TestResponse) (bool, int) {
	if response.ResponseText == nil || question.CorrectAnswer == "" {
		return false, 0
	}

	// Simple string comparison (case-insensitive)
	// In a real system, you might want more sophisticated text matching
	responseText := *response.ResponseText
	if len(responseText) == 0 {
		return false, 0
	}

	// For now, do exact match (case-insensitive)
	// You could enhance this with fuzzy matching, keyword matching, etc.
	isCorrect := responseText == question.CorrectAnswer

	if isCorrect {
		return true, 1
	}

	return false, 0
}

// calculateSubjectScoresFromMarks calculates average scores from stored StudentTestMark records
func (p *DbPlugin) calculateSubjectScoresFromMarks(studentMarks []models.StudentTestMark) map[string]models.SubjectScoreInfo {
	result := make(map[string]models.SubjectScoreInfo)

	if len(studentMarks) == 0 {
		return result
	}

	// Aggregate marks by subject and topic across all students
	subjectTotals := make(map[string]struct {
		totalScore  int
		count       int
		topicTotals map[string]struct {
			totalScore int
			count      int
		}
	})

	for _, mark := range studentMarks {
		if mark.SubjectMarks == nil {
			continue
		}

		for subjectName, subjectMark := range mark.SubjectMarks {
			// Initialize subject if not exists
			if _, exists := subjectTotals[subjectName]; !exists {
				subjectTotals[subjectName] = struct {
					totalScore  int
					count       int
					topicTotals map[string]struct {
						totalScore int
						count      int
					}
				}{
					totalScore: 0,
					count:      0,
					topicTotals: make(map[string]struct {
						totalScore int
						count      int
					}),
				}
			}

			subjectTotal := subjectTotals[subjectName]
			subjectScore := subjectMark.PositiveMarks - subjectMark.NegativeMarks
			subjectTotal.totalScore += subjectScore
			subjectTotal.count++

			// Process topic marks
			for topicName, topicMark := range subjectMark.TopicMarks {
				if _, exists := subjectTotal.topicTotals[topicName]; !exists {
					subjectTotal.topicTotals[topicName] = struct {
						totalScore int
						count      int
					}{totalScore: 0, count: 0}
				}

				topicTotal := subjectTotal.topicTotals[topicName]
				topicScore := topicMark.PositiveMarks - topicMark.NegativeMarks
				topicTotal.totalScore += topicScore
				topicTotal.count++
				subjectTotal.topicTotals[topicName] = topicTotal
			}

			subjectTotals[subjectName] = subjectTotal
		}
	}

	// Calculate averages
	for subjectName, subjectTotal := range subjectTotals {
		if subjectTotal.count == 0 {
			continue
		}

		subjectAverage := float64(subjectTotal.totalScore) / float64(subjectTotal.count)
		topicAverages := make(map[string]float64)

		for topicName, topicTotal := range subjectTotal.topicTotals {
			if topicTotal.count > 0 {
				topicAverages[topicName] = float64(topicTotal.totalScore) / float64(topicTotal.count)
			}
		}

		result[subjectName] = models.SubjectScoreInfo{
			AverageScore: subjectAverage,
			TopicScores:  topicAverages,
		}
	}

	return result
}

// GetTestAnswerKey returns the answer key for a test based on user role
func (p *DbPlugin) GetTestAnswerKey(ctx context.Context, testID uint, userID uint, userRole string) (interface{}, error) {
	start := time.Now()

	slog.Info("Getting test answer key",
		"test_id", testID,
		"user_id", userID,
		"user_role", userRole,
	)

	// Get test with sections and questions
	var test models.Test
	if err := p.db.Preload("TestType").
		Preload("Sections.SectionType").
		Preload("Sections.Questions.Options").
		Preload("Sections.Questions.Topic.Chapter.Subject").
		Preload("Sections.Questions.Difficulty").
		Where("id = ?", testID).
		First(&test).Error; err != nil {
		duration := time.Since(start)
		slog.Error("Failed to find test for answer key",
			"test_id", testID,
			"error", err.Error(),
			"duration_ms", duration.Milliseconds(),
		)
		return nil, fmt.Errorf("test not found: %w", err)
	}

	if userRole == "Student" {
		return p.getStudentAnswerKey(ctx, test, userID, start)
	} else {
		return p.getAdminAnswerKey(ctx, test, start)
	}
}

func (p *DbPlugin) getStudentAnswerKey(ctx context.Context, test models.Test, userID uint, start time.Time) (*models.TestAnswerKeyForStudent, error) {
	// Get student info directly using user_id
	var student models.Student
	if err := p.db.Preload("User").Where("user_id = ?", userID).First(&student).Error; err != nil {
		return nil, fmt.Errorf("failed to get student info: %w", err)
	}

	// Get student responses for this test
	var responses []models.TestResponse
	p.db.Where("student_id = ? AND test_id = ?", student.ID, test.ID).Find(&responses)

	// Create response map for quick lookup
	responseMap := make(map[uint]*models.TestResponse)
	for i := range responses {
		responseMap[responses[i].QuestionID] = &responses[i]
	}

	var sections []models.TestAnswerKeySectionStudent
	totalScore := 0
	maxPossibleScore := 0

	for _, section := range test.Sections {
		var questions []models.TestAnswerKeyQuestionStudent

		for _, question := range section.Questions {
			maxPossibleScore += int(section.SectionType.PositiveMarks)

			var studentResponse *models.TestAnswerKeyStudentResponse
			if resp, exists := responseMap[question.ID]; exists {
				studentResponse = &models.TestAnswerKeyStudentResponse{
					SelectedOptionIDs: resp.SelectedOptionIDs,
					ResponseText:      resp.ResponseText,
					IsCorrect:         resp.IsCorrect,
					CalculatedScore:   resp.CalculatedScore,
					WasAttempted:      true,
				}
				if resp.CalculatedScore != nil {
					totalScore += *resp.CalculatedScore
				}
			} else {
				score := 0
				studentResponse = &models.TestAnswerKeyStudentResponse{
					SelectedOptionIDs: []int{},
					ResponseText:      nil,
					IsCorrect:         false,
					CalculatedScore:   &score,
					WasAttempted:      false,
				}
			}

			optionsForGet := make([]models.OptionForGet, len(question.Options))
			for i, option := range question.Options {
				optionsForGet[i] = models.OptionForGet{
					OptionID:       option.ID,
					OptionText:     option.OptionText,
					OptionImageURL: option.OptionImageURL,
					IsCorrect:      option.IsCorrect,
				}
			}

			questionInfo := models.TestAnswerKeyQuestionStudent{
				QuestionID:      question.ID,
				Text:            question.Text,
				QuestionType:    question.QuestionType,
				ImageUrl:        stringToPointer(question.ImageUrl),
				FileUrl:         stringToPointer(question.FileUrl),
				Options:         optionsForGet, // Include correct answers and option IDs
				CorrectAnswer:   stringToPointer(question.CorrectAnswer),
				StudentResponse: studentResponse,
				TopicName:       question.Topic.Name,
				DifficultyName:  question.Difficulty.Name,
				SubjectName:     question.Topic.Chapter.Subject.Name,
			}
			questions = append(questions, questionInfo)
		}

		sectionInfo := models.TestAnswerKeySectionStudent{
			SectionID:   section.ID,
			SectionName: section.Name,
			DisplayName: section.DisplayName,
			Questions:   questions,
		}
		sections = append(sections, sectionInfo)
	}

	duration := time.Since(start)
	slog.Info("Student answer key retrieved successfully",
		"test_id", test.ID,
		"student_id", student.ID,
		"total_score", totalScore,
		"max_possible_score", maxPossibleScore,
		"duration_ms", duration.Milliseconds(),
	)

	result := &models.TestAnswerKeyForStudent{
		TestID:           test.ID,
		TestName:         test.Name,
		StudentID:        student.ID,
		StudentName:      student.User.FullName,
		TotalScore:       totalScore,
		MaxPossibleScore: maxPossibleScore,
		Sections:         sections,
		Message:          fmt.Sprintf("Answer key retrieved for student with score %d/%d", totalScore, maxPossibleScore),
	}

	return result, nil
}

func (p *DbPlugin) getAdminAnswerKey(ctx context.Context, test models.Test, start time.Time) (*models.TestAnswerKeyForAdmin, error) {
	var sections []models.TestAnswerKeySectionAdmin
	maxPossibleScore := 0

	for _, section := range test.Sections {
		var questions []models.TestAnswerKeyQuestionAdmin

		for _, question := range section.Questions {
			maxPossibleScore += int(section.SectionType.PositiveMarks)

			optionsForGet := make([]models.OptionForGet, len(question.Options))
			for i, option := range question.Options {
				optionsForGet[i] = models.OptionForGet{
					OptionID:       option.ID,
					OptionText:     option.OptionText,
					OptionImageURL: option.OptionImageURL,
					IsCorrect:      option.IsCorrect,
				}
			}

			questionInfo := models.TestAnswerKeyQuestionAdmin{
				QuestionID:     question.ID,
				Text:           question.Text,
				QuestionType:   question.QuestionType,
				ImageUrl:       stringToPointer(question.ImageUrl),
				FileUrl:        stringToPointer(question.FileUrl),
				Options:        optionsForGet, // Include correct answers and option IDs
				CorrectAnswer:  stringToPointer(question.CorrectAnswer),
				TopicName:      question.Topic.Name,
				DifficultyName: question.Difficulty.Name,
				SubjectName:    question.Topic.Chapter.Subject.Name,
			}
			questions = append(questions, questionInfo)
		}

		sectionInfo := models.TestAnswerKeySectionAdmin{
			SectionID:   section.ID,
			SectionName: section.Name,
			DisplayName: section.DisplayName,
			Questions:   questions,
		}
		sections = append(sections, sectionInfo)
	}

	duration := time.Since(start)
	slog.Info("Admin answer key retrieved successfully",
		"test_id", test.ID,
		"max_possible_score", maxPossibleScore,
		"duration_ms", duration.Milliseconds(),
	)

	result := &models.TestAnswerKeyForAdmin{
		TestID:           test.ID,
		TestName:         test.Name,
		TestType:         test.TestType.Name,
		MaxPossibleScore: maxPossibleScore,
		Sections:         sections,
		Message:          fmt.Sprintf("Answer key retrieved for test '%s' with %d questions", test.Name, maxPossibleScore),
	}

	return result, nil
}

// stringToPointer converts a string to a pointer to string.
// If the string is empty, it returns nil.
func stringToPointer(s string) *string {
	if s == "" {
		return nil
	}
	return &s
}
